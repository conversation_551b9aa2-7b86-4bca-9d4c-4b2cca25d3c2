# AI Agent Analysis Companion - Technical Specifications

## 🎯 Project Overview
Implementation of an AI agent analysis companion for the volo-app project using the mcp-use-ts library. The system will provide intelligent code analysis, project insights, and development assistance through Model Context Protocol (MCP) integration.

## 🏗️ Architecture Overview

### Core Components
1. **MCP Servers** - Expose tools and data sources to AI agents
2. **AI Agent Service** - LangChain.js + mcp-use-ts integration
3. **API Layer** - Hono backend endpoints for agent communication
4. **Frontend Interface** - React components for user interaction
5. **Port Management** - Extended dynamic port allocation system

### Technology Stack Integration
- **Existing:** React + Vite, Hono API, Firebase Auth, PostgreSQL, Tailwind + ShadCN
- **New:** mcp-use-ts, LangChain.js, MCP servers, AI agent infrastructure

## 🔧 Implementation Details (Based on Real MCP Research)

### 1. MCP Server Configuration (Proven Patterns)

#### Development Mode - Following mcp-use-ts Examples
```javascript
// scripts/mcp-config.dev.js - Based on real implementations
export const devMCPConfig = {
  mcpServers: {
    // Filesystem server - proven pattern from mcp-use-ts examples
    filesystem: {
      command: 'npx',
      args: ['@modelcontextprotocol/server-filesystem'],
      env: {
        ROOT_PATH: process.cwd(),
        ALLOWED_EXTENSIONS: '.js,.ts,.tsx,.json,.md,.yml,.yaml'
      }
    },
    // Browser automation - from browser_use.ts example
    playwright: {
      command: 'npx',
      args: ['@playwright/mcp@latest'],
      env: {
        PLAYWRIGHT_HEADLESS: 'true'
      }
    },
    // HTTP-based MCP server - from http_example.ts
    codeAnalysis: {
      url: 'http://localhost:${MCP_CODE_ANALYSIS_PORT}',
      transport: 'http'
    },
    // Everything server for development - from mcp_everything.ts
    everything: {
      command: 'npx',
      args: ['-y', '@modelcontextprotocol/server-everything']
    }
  }
}
```

#### Production Mode
```javascript
// scripts/mcp-config.prod.js
export const prodMCPConfig = {
  mcpServers: {
    filesystem: {
      command: 'npx',
      args: ['@modelcontextprotocol/server-filesystem'],
      env: { 
        ROOT_PATH: '/app',
        ALLOWED_EXTENSIONS: '.js,.ts,.tsx,.json,.md'
      }
    },
    // Production servers would be deployed as separate services
    codeAnalysis: {
      url: 'https://code-analysis-mcp.your-domain.com',
      auth: { type: 'bearer', token: '${MCP_AUTH_TOKEN}' }
    },
    projectInsights: {
      url: 'https://project-insights-mcp.your-domain.com', 
      auth: { type: 'bearer', token: '${MCP_AUTH_TOKEN}' }
    }
  }
}
```

### 2. Port Management Extension

#### Extended Port Allocation
```javascript
// scripts/port-manager.js - Extension
export async function getAvailablePortsWithMCP() {
  const basePorts = await getAvailablePorts(); // Existing function
  
  // Extend with MCP server ports
  const mcpPorts = {
    mcpCodeAnalysis: basePorts.firebaseUI + 1,    // 5505
    mcpProjectInsights: basePorts.firebaseUI + 2, // 5506
    mcpDevTools: basePorts.firebaseUI + 3,        // 5507
    agentService: basePorts.firebaseUI + 4,       // 5508
    mcpReserved: basePorts.firebaseUI + 5         // 5509
  };
  
  return { ...basePorts, ...mcpPorts };
}

export function updateEnvWithMCPPorts(availablePorts) {
  const envUpdates = {
    MCP_CODE_ANALYSIS_PORT: availablePorts.mcpCodeAnalysis,
    MCP_PROJECT_INSIGHTS_PORT: availablePorts.mcpProjectInsights,
    MCP_DEV_TOOLS_PORT: availablePorts.mcpDevTools,
    AGENT_SERVICE_PORT: availablePorts.agentService
  };
  
  return updateServerEnvWithPorts(envUpdates);
}
```

### 3. Database Schema Implementation

#### Schema Files Structure
```
server/src/schema/
├── users.ts              # Existing
├── agent-sessions.ts     # New
├── analysis-results.ts   # New
├── mcp-configurations.ts # New
└── index.ts             # Updated exports
```

#### Agent Sessions Schema
```typescript
// server/src/schema/agent-sessions.ts
import { pgTable, uuid, text, timestamp, jsonb, boolean } from 'drizzle-orm/pg-core';
import { users } from './users';

export const agentSessions = pgTable('agent_sessions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: text('user_id').notNull().references(() => users.id),
  sessionName: text('session_name').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  status: text('status').default('active'),
  configuration: jsonb('configuration'),
  mcpServers: jsonb('mcp_servers'), // Active MCP servers for this session
});

export type AgentSession = typeof agentSessions.$inferSelect;
export type NewAgentSession = typeof agentSessions.$inferInsert;
```

#### Analysis Results Schema
```typescript
// server/src/schema/analysis-results.ts
import { pgTable, uuid, text, timestamp, jsonb, integer } from 'drizzle-orm/pg-core';
import { agentSessions } from './agent-sessions';

export const analysisResults = pgTable('analysis_results', {
  id: uuid('id').primaryKey().defaultRandom(),
  sessionId: uuid('session_id').notNull().references(() => agentSessions.id),
  analysisType: text('analysis_type').notNull(),
  inputData: jsonb('input_data').notNull(),
  outputData: jsonb('output_data').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  executionTimeMs: integer('execution_time_ms'),
  mcpServerUsed: text('mcp_server_used'),
  success: boolean('success').default(true),
  errorMessage: text('error_message'),
});

export type AnalysisResult = typeof analysisResults.$inferSelect;
export type NewAnalysisResult = typeof analysisResults.$inferInsert;
```

### 4. AI Agent Service Implementation (Real mcp-use-ts Patterns)

#### Core Agent Service - Based on Actual Examples
```typescript
// server/src/services/ai-agent.ts - Following mcp-use-ts patterns
import { ChatOpenAI } from '@langchain/openai';
import { MCPAgent, MCPClient, streamEventsToAISDK, streamEventsToAISDKWithTools } from 'mcp-use';
import { getEnv } from '../lib/env';

export class AIAgentService {
  private client: MCPClient;
  private agent: MCPAgent;

  constructor(mcpConfig: any) {
    // Pattern from examples/browser_use.ts and examples/filesystem_use.ts
    this.client = MCPClient.fromDict(mcpConfig);

    const llm = new ChatOpenAI({
      modelName: 'gpt-4o',
      apiKey: getEnv('OPENAI_API_KEY')
    });

    // Pattern from examples with maxSteps configuration
    this.agent = new MCPAgent({
      llm,
      client: this.client,
      maxSteps: 30,  // Increased based on browser_use.ts example
      useServerManager: true  // For dynamic server management
    });
  }

  // Basic analysis - pattern from examples
  async analyzeCode(code: string, analysisType: string) {
    const query = `Analyze this ${analysisType}: ${code}`;
    return await this.agent.run(query);
  }

  // Project insights with filesystem access
  async getProjectInsights(projectPath: string) {
    const query = `Analyze the project structure at ${projectPath} and provide insights about the codebase, architecture, and potential improvements.`;
    return await this.agent.run(query);
  }

  // Streaming analysis - pattern from examples/ai_sdk_example.ts
  async streamAnalysis(query: string) {
    return this.agent.stream(query);
  }

  // AI SDK compatible streaming - real implementation from mcp-use-ts
  async streamAnalysisForAISDK(query: string) {
    const streamEvents = this.agent.streamEvents(query);
    return streamEventsToAISDKWithTools(streamEvents);
  }

  // Dynamic server addition - pattern from add_server_tool.ts
  async addMCPServer(serverName: string, config: any) {
    // This would use the dynamic server management capabilities
    // Implementation would follow the add_server_tool.ts pattern
  }

  async cleanup() {
    await this.client.closeAllSessions();
  }
}
```

### 5. API Endpoints Implementation

#### Agent API Routes
```typescript
// server/src/routes/agent.ts
import { Hono } from 'hono';
import { AIAgentService } from '../services/ai-agent';
import { getDatabase } from '../lib/db';
import { agentSessions, analysisResults } from '../schema';

const agentRoutes = new Hono();

agentRoutes.post('/analyze', async (c) => {
  const user = c.get('user');
  const { code, analysisType, sessionId } = await c.req.json();
  
  try {
    const agentService = new AIAgentService(getMCPConfig());
    const startTime = Date.now();
    
    const result = await agentService.analyzeCode(code, analysisType);
    const executionTime = Date.now() - startTime;
    
    // Save to database
    const db = await getDatabase();
    await db.insert(analysisResults).values({
      sessionId,
      analysisType,
      inputData: { code, analysisType },
      outputData: { result },
      executionTimeMs: executionTime,
      success: true
    });
    
    await agentService.cleanup();
    
    return c.json({ result, executionTime });
  } catch (error) {
    return c.json({ error: error.message }, 500);
  }
});

export default agentRoutes;
```

### 6. Frontend Components

#### Main Agent Dashboard
```typescript
// ui/src/components/agent/AgentDashboard.tsx
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

export function AgentDashboard() {
  const [code, setCode] = useState('');
  const [analysis, setAnalysis] = useState('');
  const [loading, setLoading] = useState(false);
  
  const analyzeCode = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/agent/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          code, 
          analysisType: 'general',
          sessionId: 'current-session' 
        })
      });
      
      const result = await response.json();
      setAnalysis(result.result);
    } catch (error) {
      console.error('Analysis failed:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Code Input</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            placeholder="Paste your code here for analysis..."
            className="min-h-[300px]"
          />
          <Button 
            onClick={analyzeCode} 
            disabled={loading || !code}
            className="mt-4"
          >
            {loading ? 'Analyzing...' : 'Analyze Code'}
          </Button>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Analysis Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="min-h-[300px] p-4 bg-gray-50 rounded">
            {analysis || 'Analysis results will appear here...'}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

## 🚀 Deployment Considerations

### Development Environment
- All MCP servers run as local processes
- Dynamic port allocation prevents conflicts
- Hot reload support for agent service
- Local database with embedded PostgreSQL

### Production Environment  
- MCP servers deployed as separate Cloudflare Workers or external services
- Agent service integrated into main Hono API
- External PostgreSQL database
- Environment-specific configuration management

## 📋 Implementation Checklist

### Phase 2: Data Layer
- [ ] Create database schemas
- [ ] Add migration scripts
- [ ] Test with local and production databases
- [ ] Update database connection logic

### Phase 3A: Backend MCP Integration
- [ ] Install mcp-use-ts and dependencies
- [ ] Create MCP server configurations
- [ ] Implement AI agent service
- [ ] Add API endpoints
- [ ] Extend port management
- [ ] Configure environment detection

### Phase 3B: Frontend Development
- [ ] Create agent dashboard components
- [ ] Implement real-time communication
- [ ] Add to main navigation
- [ ] Create analysis visualization
- [ ] Implement session management

### Phase 4: Code Review
- [ ] Security review
- [ ] Performance review
- [ ] UI/UX review
- [ ] Code quality review

### Phase 5: Integration & Testing
- [ ] End-to-end testing
- [ ] Production deployment testing
- [ ] Port conflict resolution testing
- [ ] Authentication flow testing
- [ ] Performance optimization
- [ ] Documentation completion
